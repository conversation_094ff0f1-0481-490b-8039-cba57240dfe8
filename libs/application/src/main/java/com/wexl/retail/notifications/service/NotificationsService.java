package com.wexl.retail.notifications.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.classroom.core.model.Classroom;
import com.wexl.retail.classroom.core.repository.ClassroomRepository;
import com.wexl.retail.classroom.core.service.ClassroomService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.communications.circulars.service.CommunicationFeature;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.erp.attendance.domain.SectionAttendance;
import com.wexl.retail.erp.attendance.domain.SectionAttendanceDetails;
import com.wexl.retail.globalprofile.model.AppTemplate;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.messagetemplate.category.model.MessageTemplateCategory;
import com.wexl.retail.messagetemplate.category.repository.MessageTemplateCategoryRepository;
import com.wexl.retail.messagetemplate.model.MessageTemplate;
import com.wexl.retail.messagetemplate.repository.MessageTemplateRepository;
import com.wexl.retail.messagetemplate.service.MessageTemplateService;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.mlp.service.MlpService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationDto.NotificationRequest;
import com.wexl.retail.notifications.dto.NotificationMessageType;
import com.wexl.retail.notifications.model.*;
import com.wexl.retail.notifications.repository.*;
import com.wexl.retail.organization.admin.teacher.TeacherService;
import com.wexl.retail.organization.handler.EntityHandler;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.domain.SectionStatus;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.staff.model.Staff;
import com.wexl.retail.staff.repository.StaffRepository;
import com.wexl.retail.staff.service.StaffService;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.ValidationUtils;
import jakarta.transaction.Transactional;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class NotificationsService {

  private final NotificationRepository notificationRepository;
  private final SectionNotificationRepository sectionNotificationRepository;
  private final StudentNotificationRepository studentNotificationRepository;

  private final OrganizationRepository organizationRepository;

  private final ClassroomService classroomService;

  private final SectionService sectionservice;

  private final UserRepository userRepository;

  private final StudentService studentService;

  private final UserService userService;

  private final DateTimeUtil dateTimeUtil;

  private final ValidationUtils validationUtils;
  private final TeacherService teacherService;

  private final AuthService authService;
  private final StorageService storageService;
  private final UserRoleHelper userRoleHelper;
  private final SectionService sectionService;
  private final MessageTemplateService messageTemplateService;
  private final MessageTemplateRepository messageTemplateRepository;
  private final ClassroomRepository classroomRepository;
  private final TeacherRepository teacherRepository;
  private final GuardianService guardianService;
  private final StudentAuthService studentAuthService;
  private final ContentService contentService;
  private final SectionRepository sectionRepository;
  private final NotificationLogRepository notificationLogRepository;
  private final StaffNotificationRepository staffNotificationRepository;

  private final MessageTemplateCategoryRepository messageTemplateCategoryRepository;

  private final EventNotificationService eventNotificationService;

  private final MlpService mlpService;
  private final StaffService staffService;
  private final StaffRepository staffRepository;

  public final List<EntityHandler<Notification>> notificationHandlers;
  private static final String WEXL_INTERNAL = "wexl-internal";
  private final String present = "present";
  private final String absent = "absent";
  private static final String AFTERNOON = "afternoon";
  private static final String SMS_MESSAGE_TEMPLATE = "66ebf74cd6fc0552c172f9a2";
  private static final String CIRCULAR_TITLE = "Circular Notification";
  private static final Set<NotificationType> excludedTypes =
      EnumSet.of(
          NotificationType.ORGANIZATION,
          NotificationType.SECTION,
          NotificationType.GRADE,
          NotificationType.LEAVE_APPROVED,
          NotificationType.LEAVE_DISAPPROVED,
          NotificationType.APPOINTMENT_APPROVED,
          NotificationType.APPOINTMENT_DISAPPROVED,
          NotificationType.STAFF_APPOINTMENT,
          NotificationType.GATEPASS_APPROVED,
          NotificationType.GATEPASS_DISAPPROVED);

  public Long createNotificationForOrganization(
      String orgSlug,
      NotificationDto.NotificationRequest notificationRequest,
      String teacherAuthId,
      boolean allClassRooms,
      Long date) {
    var notification = buildOrganizationNotification(notificationRequest, orgSlug, teacherAuthId);
    notificationHandlers.getFirst().postSave(notification);
    eventNotificationService.triggerNotification(notificationRequest, orgSlug, date);
    return saveNotification(notification, notificationRequest, orgSlug, allClassRooms);
  }

  public void createNotificationByTeacher(
      String orgSlug,
      NotificationDto.NotificationRequest notificationRequest,
      String teacherAuthId,
      boolean allClassRooms,
      Long date) {
    var notification = buildNotification(notificationRequest, orgSlug, teacherAuthId);

    saveNotification(notification, notificationRequest, orgSlug, allClassRooms);
    notificationHandlers.getFirst().postSave(notification);
    eventNotificationService.triggerNotification(notificationRequest, orgSlug, date);
  }

  public void createNotificationByStaff(
      String orgSlug,
      NotificationDto.NotificationRequest notificationRequest,
      String staffAuthId,
      boolean allClassRooms) {
    var notification = buildUserNotification(notificationRequest, orgSlug, staffAuthId);
    saveNotification(notification, notificationRequest, orgSlug, allClassRooms);
    saveStaffNotification(notification);
    notificationHandlers.getFirst().postSave(notification);
    eventNotificationService.triggerNotification(notificationRequest, orgSlug, null);
  }

  public void createNotificationByTeacher(
      String orgSlug,
      NotificationDto.NotificationRequest request,
      String teacherAuthId,
      boolean allClassRooms) {
    var notification = buildNotification(request, orgSlug, teacherAuthId);
    saveNotification(notification, request, orgSlug, allClassRooms);
    notificationHandlers.getFirst().postSave(notification);
    eventNotificationService.triggerNotification(request, orgSlug, null);
  }

  public void createNotificationForStudent(
      String orgSlug, NotificationDto.NotificationRequest request, String userAuthId) {
    Optional<User> optionalUseruser = userRepository.findByAuthUserId(userAuthId);
    User user = optionalUseruser.get();
    var notification = buildUserNotification(request, orgSlug, userAuthId);
    buildStudentNotification(List.of(user.getStudentInfo()), notification, orgSlug);
    saveNotification(notification, request, orgSlug, false);
    notificationHandlers.getFirst().postSave(notification);
    eventNotificationService.triggerNotification(request, orgSlug, null);
  }

  public void triggerAttendanceNotification(
      SectionAttendance sectionAttendance, String sessionType, Long date) {
    try {
      List<Student> morningAbsentees = new ArrayList<>();
      List<Student> afterNoonAbsentees = new ArrayList<>();
      if (!sessionType.equals(AFTERNOON)) {
        morningAbsentees =
            sectionAttendance.getAttendanceDetails().stream()
                .filter(attendanceDetail -> absent.equals(attendanceDetail.getAttendanceStatus()))
                .map(SectionAttendanceDetails::getStudent)
                .toList();
      } else {
        afterNoonAbsentees =
            sectionAttendance.getAttendanceDetails().stream()
                .filter(
                    attendanceDetail ->
                        absent.equals(attendanceDetail.getAfternoonAttendanceStatus()))
                .map(SectionAttendanceDetails::getStudent)
                .toList();
      }
      var teacherUser = authService.getUserDetails();

      final MessageTemplate absenteesMessageTemplate =
          messageTemplateService.validateAndGetMessageTemplate(
              sectionAttendance.getOrg().getSlug(), "Absentees");
      if (absenteesMessageTemplate == null) {
        return;
      }
      if (!morningAbsentees.isEmpty()) {
        sendAbsenteeNotification(
            morningAbsentees,
            absenteesMessageTemplate,
            sectionAttendance,
            teacherUser.getAuthUserId(),
            date);
      } else if (!afterNoonAbsentees.isEmpty()) {
        var afternoonMsg =
            messageTemplateRepository.findBySmsDltTemplateIdAndOrgSlug(
                SMS_MESSAGE_TEMPLATE, sectionAttendance.getOrg().getSlug());
        sendAbsenteeNotification(
            afterNoonAbsentees,
            afternoonMsg.get(),
            sectionAttendance,
            teacherUser.getAuthUserId(),
            date);
      }
    } catch (Exception e) {
      log.error("Unable to trigger attendance notification. Message: [" + e.getMessage() + "]", e);
    }
  }

  private void sendAbsenteeNotification(
      List<Student> absenteeStudents,
      MessageTemplate absenteesMessageTemplate,
      SectionAttendance sectionAttendance,
      String authUserId,
      Long date) {
    NotificationRequest notificationRequest =
        NotificationRequest.builder()
            .notificationType(com.wexl.retail.notifications.model.NotificationType.INDIVIDUAL)
            .message(absenteesMessageTemplate.getMessage())
            .title("Absentee Notification")
            .sectionUuids(null)
            .studentIds(absenteeStudents.stream().map(Student::getId).toList())
            .messageTemplateId(absenteesMessageTemplate.getId())
            .categoryId(absenteesMessageTemplate.getMessageTemplateCategory().getId())
            .build();
    createNotificationByTeacher(
        sectionAttendance.getOrg().getSlug(), notificationRequest, authUserId, false, date);
  }

  private Long saveNotification(
      Notification notification,
      NotificationDto.NotificationRequest notificationRequest,
      String orgSlug,
      boolean allClassRooms) {
    if (NotificationType.CLASSROOM.name().equals(notificationRequest.notificationType().name())
        && Objects.nonNull(notificationRequest.classroomIds())) {
      buildClassroomNotification(notificationRequest, orgSlug, notification, allClassRooms);
    } else if (NotificationType.SECTION.name().equals(notificationRequest.notificationType().name())
        && Objects.nonNull(notificationRequest.sectionUuids())) {
      buildSectionNotification(notificationRequest, orgSlug, notification);
    } else if (NotificationType.GRADE.name().equals(notificationRequest.notificationType().name())
        && Objects.nonNull(notificationRequest.gradeSlug())) {
      buildGradeNotification(notificationRequest, orgSlug, notification);
    } else if (NotificationType.ORGANIZATION
            .name()
            .equals(notificationRequest.notificationType().name())
        && Objects.nonNull(notificationRequest.orgSlugs())) {
      buildOrganizationNotification(notificationRequest, orgSlug, notificationRequest.userAuthId());
    } else if (NotificationType.LEAVE_APPROVED
            .name()
            .equals(notificationRequest.notificationType().name())
        || NotificationType.LEAVE_DISAPPROVED
            .name()
            .equals(notificationRequest.notificationType().name())) {
      var student = studentService.getStudentById(notificationRequest.studentIds().getFirst());
      buildStudentNotification(List.of(student), notification, orgSlug);
    } else if (NotificationType.APPOINTMENT_APPROVED
            .name()
            .equals(notificationRequest.notificationType().name())
        || NotificationType.APPOINTMENT_DISAPPROVED
            .name()
            .equals(notificationRequest.notificationType().name())) {
      var student = studentService.getStudentById(notificationRequest.studentIds().getFirst());
      buildStudentNotification(List.of(student), notification, orgSlug);
    } else if (NotificationType.STAFF_APPOINTMENT
        .name()
        .equals(notificationRequest.notificationType().name())) {
      buildUserNotification(notificationRequest, orgSlug, notificationRequest.staffAuthId());
    } else if (NotificationType.GATEPASS_APPROVED
            .name()
            .equals(notificationRequest.notificationType().name())
        || NotificationType.GATEPASS_DISAPPROVED
            .name()
            .equals(notificationRequest.notificationType().name())) {
      buildUserNotification(notificationRequest, orgSlug, notificationRequest.userAuthId());
    } else if (NotificationType.INFIRMARY
        .name()
        .equals(notificationRequest.notificationType().name())) {
      List<Teacher> teachers = teacherRepository.findAllById(notificationRequest.teacherIds());
      buildTeacherNotification(teachers, notification, orgSlug);
      var student = studentService.getStudentById(notificationRequest.studentIds().getFirst());
      buildStudentNotification(List.of(student), notification, orgSlug);
    } else if (NotificationType.FORUM
        .name()
        .equals(notificationRequest.notificationType().name())) {
      if (Objects.nonNull(notificationRequest.studentIds())
          && !notificationRequest.studentIds().isEmpty()) {
        var student = studentService.getStudentById(notificationRequest.studentIds().getFirst());
        buildStudentNotification(List.of(student), notification, orgSlug);
      }
    } else {
      if (Objects.nonNull(notificationRequest.studentIds())
          && !notificationRequest.studentIds().isEmpty()) {
        var students =
            studentService.getStudentsByIdsAndOrgSlug(
                orgSlug, notificationRequest.studentIds(), false);
        if (!students.isEmpty()) {
          buildStudentNotification(students, notification, orgSlug);
          notification.setNotificationType(NotificationType.INDIVIDUAL);
        }
      }
    }
    var user = authService.getUserDetails();
    if (!excludedTypes.contains(notification.getNotificationType())) {
      if (userRoleHelper.isTeacher(user)) {
        List<Teacher> allTeachers = new ArrayList<>(teacherRepository.getAllAdminsByOrg(orgSlug));
        if (userRoleHelper.isManager(user)) {
          allTeachers.addAll(
              teacherRepository.getAllTeacherByOrgSlug(orgSlug, AppTemplate.TEACHER.toString()));
          buildTeacherNotification(allTeachers, notification, orgSlug);
        } else {
          buildTeacherNotification(allTeachers, notification, orgSlug);
        }
      } else if (AuthUtil.isStudent(user)
          && !notificationRequest
              .notificationType()
              .name()
              .equals(NotificationType.STAFF_APPOINTMENT.name())) {
        buildTeacherNotification(List.of(notification.getCreatedBy()), notification, orgSlug);
      }
    }
    var savedNotification = notificationRepository.save(notification);
    return savedNotification.getId();
  }

  private void buildOrgNotification(
      NotificationRequest notificationRequest, String orgSlug, Notification notification) {
    var allSections =
        sectionRepository.findAllByOrganizationAndDeletedAtIsNullAndStatusOrderByName(
            orgSlug, SectionStatus.ACTIVE);
    var sectionUuids = allSections.stream().map(section -> section.getUuid().toString()).toList();
    var newNotificationRequest =
        NotificationRequest.builder()
            .sectionUuids(sectionUuids)
            .orgSlugs(notificationRequest.orgSlugs())
            .categoryId(notificationRequest.categoryId())
            .notificationType(notificationRequest.notificationType())
            .title(notificationRequest.title())
            .messageTemplateId(notificationRequest.messageTemplateId())
            .attachment(notificationRequest.attachment())
            .message(notificationRequest.message())
            .link(notificationRequest.link())
            .build();
    buildSectionNotification(newNotificationRequest, orgSlug, notification);
  }

  private void buildGradeNotification(
      NotificationDto.NotificationRequest notificationRequest,
      String orgSlug,
      Notification notification) {
    List<String> sections =
        sectionRepository
            .findAllByGradeSlugInAndOrganizationAndStatus(
                Collections.singletonList(notificationRequest.gradeSlug()),
                orgSlug,
                SectionStatus.ACTIVE)
            .stream()
            .map(section -> section.getUuid().toString())
            .toList();
    var students = studentService.getStudentsBySectionUuidsAndOrgSlug(sections, orgSlug);
    var teachers =
        teacherRepository.getSectionTeachersBySectionUuids(
            sections.stream().map(UUID::fromString).toList());
    buildTeacherNotification(teachers, notification, orgSlug);
    buildStudentNotification(students, notification, orgSlug);
    List<SectionNotification> sectionNotifications = new ArrayList<>();
    sections.forEach(
        sectionUuid ->
            sectionNotifications.add(
                SectionNotification.builder()
                    .notification(notification)
                    .section(sectionservice.findByUuid(sectionUuid))
                    .orgSlug(orgSlug)
                    .build()));
    notification.setSectionNotifications(sectionNotifications);
    notification.setGradeSlug(notificationRequest.gradeSlug());
  }

  private Notification buildNotification(
      NotificationDto.NotificationRequest notificationRequest,
      String orgSlug,
      String teacherAuthId) {
    var user = userRepository.getUserByAuthUserId(teacherAuthId);
    return Notification.builder()
        .title(notificationRequest.title())
        .message(notificationRequest.message())
        .attachments(notificationRequest.attachment())
        .link(notificationRequest.link())
        .notificationType(NotificationType.valueOf(notificationRequest.notificationType().name()))
        .organization(organizationRepository.findBySlug(orgSlug))
        .gradeSlug(notificationRequest.gradeSlug())
        .createdBy(user.getTeacherInfo())
        .orgSlug(orgSlug)
        .feature(notificationRequest.feature())
        .categoryId(notificationRequest.categoryId())
        .messageTemplate(
            notificationRequest.messageTemplateId() != null
                ? messageTemplateService.getMessageTemplate(
                    user.getOrganization(), notificationRequest.messageTemplateId())
                : null)
        .fromDate(
            Objects.nonNull(notificationRequest.fromDate())
                ? dateTimeUtil.convertEpochToTimestamp(notificationRequest.fromDate())
                : null)
        .toDate(
            Objects.nonNull(notificationRequest.toDate())
                ? dateTimeUtil.convertEpochToTimestamp(notificationRequest.toDate())
                : null)
        .build();
  }

  @Async
  private Notification buildOrganizationNotification(
      NotificationDto.NotificationRequest notificationRequest,
      String orgSlug,
      String teacherAuthId) {

    var teacherUser = userRepository.getUserByAuthUserId(teacherAuthId);
    Notification notification =
        Notification.builder()
            .title(notificationRequest.title())
            .message(notificationRequest.message())
            .attachments(notificationRequest.attachment())
            .link(notificationRequest.link())
            .notificationType(
                NotificationType.valueOf(notificationRequest.notificationType().name()))
            .createdBy(
                (teacherAuthId != null
                        && !teacherAuthId.isBlank()
                        && teacherUser != null)
                    ? teacherUser.getTeacherInfo()
                    : authService.getUserDetails().getTeacherInfo())
            .orgSlug(orgSlug)
            .feature(notificationRequest.feature())
            .categoryId(notificationRequest.categoryId())
            .messageTemplate(
                notificationRequest.messageTemplateId() != null
                    ? messageTemplateService.getMessageTemplate(
                        orgSlug, notificationRequest.messageTemplateId())
                    : null)
            .fromDate(
                Objects.nonNull(notificationRequest.fromDate())
                    ? dateTimeUtil.convertEpochToTimestamp(notificationRequest.fromDate())
                    : null)
            .toDate(
                Objects.nonNull(notificationRequest.toDate())
                    ? dateTimeUtil.convertEpochToTimestamp(notificationRequest.toDate())
                    : null)
            .build();

    List<User> users = userRepository.findAllWithTeacherAndStudentInfoByOrganization(orgSlug);

    List<TeacherNotification> teacherNotifications = new ArrayList<>();
    List<StudentNotification> studentNotifications = new ArrayList<>();

    for (User user : users) {
      if (user.getTeacherInfo() != null) {
        teacherNotifications.add(
            TeacherNotification.builder()
                .notification(notification)
                .teacher(user.getTeacherInfo())
                .orgSlug(orgSlug)
                .build());
      } else if (user.getStudentInfo() != null) {
        studentNotifications.add(
            StudentNotification.builder()
                .notification(notification)
                .student(user.getStudentInfo())
                .orgSlug(orgSlug)
                .build());
      }
    }
    notification.setTeacherNotifications(teacherNotifications);
    notification.setStudentNotifications(studentNotifications);

    return notification;
  }

  private Notification buildUserNotification(
      NotificationDto.NotificationRequest notificationRequest, String orgSlug, String authId) {
    var user = userRepository.getUserByAuthUserId(authId);
    return Notification.builder()
        .title(notificationRequest.title())
        .message(notificationRequest.message())
        .attachments(notificationRequest.attachment())
        .link(notificationRequest.link())
        .notificationType(NotificationType.valueOf(notificationRequest.notificationType().name()))
        .organization(organizationRepository.findBySlug(orgSlug))
        .gradeSlug(notificationRequest.gradeSlug())
        .user(user)
        .orgSlug(orgSlug)
        .feature(notificationRequest.feature())
        .categoryId(notificationRequest.categoryId())
        .messageTemplate(
            notificationRequest.messageTemplateId() != null
                ? messageTemplateService.getMessageTemplate(
                    user.getOrganization(), notificationRequest.messageTemplateId())
                : null)
        .fromDate(
            Objects.nonNull(notificationRequest.fromDate())
                ? dateTimeUtil.convertEpochToTimestamp(notificationRequest.fromDate())
                : null)
        .toDate(
            Objects.nonNull(notificationRequest.toDate())
                ? dateTimeUtil.convertEpochToTimestamp(notificationRequest.toDate())
                : null)
        .build();
  }

  private void saveStaffNotification(Notification notification) {
    Optional<Staff> staffOptional = staffRepository.findByUser(notification.getUser());
    if (staffOptional.isEmpty()) {
      return;
    }
    Staff staff = staffOptional.get();
    staffNotificationRepository.save(
        StaffNotification.builder()
            .notification(notification)
            .user(notification.getUser())
            .staff(staff)
            .orgSlug(notification.getOrgSlug())
            .build());
  }

  private void buildSectionNotification(
      NotificationDto.NotificationRequest notificationRequest,
      String orgSlug,
      Notification notification) {
    List<SectionNotification> sectionNotifications = new ArrayList<>();

    if (Objects.nonNull(notificationRequest.studentIds())) {
      var students =
          studentService.getStudentsByIdsAndOrgSlug(
              orgSlug, notificationRequest.studentIds(), true);
      buildStudentNotification(students, notification, orgSlug);
    } else {
      var students =
          studentService.getStudentsBySectionUuidsAndOrgSlug(
              notificationRequest.sectionUuids(), orgSlug);
      buildStudentNotification(students, notification, orgSlug);
    }

    List<Teacher> teachers =
        teacherRepository.getSectionTeachersBySectionUuids(
            notificationRequest.sectionUuids().stream().map(UUID::fromString).toList());
    buildTeacherNotification(teachers, notification, orgSlug);
    notificationRequest
        .sectionUuids()
        .forEach(
            sectionUuid ->
                sectionNotifications.add(
                    SectionNotification.builder()
                        .notification(notification)
                        .section(sectionservice.findByUuid(sectionUuid))
                        .orgSlug(orgSlug)
                        .build()));
    notification.setSectionNotifications(sectionNotifications);
  }

  private void buildClassroomNotification(
      NotificationDto.NotificationRequest notificationRequest,
      String orgSlug,
      Notification notification,
      boolean allClassRooms) {
    var classrooms =
        classroomService.getClassroomsByIdsAndOrgSlug(notificationRequest.classroomIds());
    List<ClassroomNotification> classroomNotifications = new ArrayList<>();

    if (Objects.nonNull(notificationRequest.studentIds())) {
      buildStudentNotification(
          studentService.getStudentsByIdsAndOrgSlug(
              orgSlug, notificationRequest.studentIds(), true),
          notification,
          orgSlug);
    } else if (allClassRooms) {
      var students =
          classroomRepository.getStudentIdsByClassRoom(notificationRequest.classroomIds());
      if (!students.isEmpty()) {
        buildStudentNotification(
            studentService.getStudentsByIdsAndOrgSlug(orgSlug, students, true),
            notification,
            orgSlug);
      }
    } else {
      classrooms.forEach(
          classroom -> buildStudentNotification(classroom.getStudents(), notification, orgSlug));
    }
    classrooms.forEach(
        classroom -> buildTeacherNotification(classroom.getTeachers(), notification, orgSlug));
    classrooms.forEach(
        classroom ->
            classroomNotifications.add(
                ClassroomNotification.builder()
                    .classroom(classroom)
                    .notification(notification)
                    .orgSlug(orgSlug)
                    .build()));
    notification.setClassroomNotifications(classroomNotifications);
  }

  private void buildStudentNotification(
      List<Student> students, Notification notification, String orgSlug) {
    MessageTemplate messageTemplate;
    var user = authService.getUserDetails();
    if (notification.getMessageTemplate() != null) {
      messageTemplate =
          messageTemplateService.getMessageTemplate(
              user.getOrganization(), notification.getMessageTemplate().getId());
    } else {
      messageTemplate = null;
    }

    List<StudentNotification> studentNotifications = new ArrayList<>();
    students.forEach(
        student ->
            studentNotifications.add(
                StudentNotification.builder()
                    .notification(notification)
                    .student(student)
                    .orgSlug(orgSlug)
                    .whatsappStatus(
                        messageTemplate != null
                            ? buildWhatsAppNotificationStatus(
                                messageTemplate.getWhatsAppTemplateId())
                            : NotificationMessageType.NONE)
                    .emailStatus(
                        messageTemplate != null
                            ? buildWhatsAppNotificationStatus(messageTemplate.getEmailTemplateId())
                            : NotificationMessageType.NONE)
                    .smsStatus(
                        messageTemplate != null
                            ? buildWhatsAppNotificationStatus(messageTemplate.getSmsDltTemplateId())
                            : NotificationMessageType.NONE)
                    .build()));
    notification.setStudentNotifications(studentNotifications);
  }

  private NotificationMessageType buildWhatsAppNotificationStatus(String templateId) {
    if (StringUtils.isNotEmpty(templateId)) {
      return NotificationMessageType.FALSE;
    }
    return NotificationMessageType.NONE;
  }

  public List<NotificationDto.StudentNotificationResponse> getStudentNotifications(
      String orgId, Long fromDateInEpoch, Long category, String appreciationMsg, Long limit) {

    LocalDate fromDate;

    if (Objects.nonNull(fromDateInEpoch)) {
      fromDate = dateTimeUtil.convertEpochToIso8601Legacy(fromDateInEpoch).toLocalDate();
    } else {
      fromDate = LocalDate.now().minusDays(365);
    }

    var studentId = authService.getStudentDetails().getStudentInfo().getId();
    var orgs =
        new ArrayList<>(
            organizationRepository.getParentOrgByChildOrgSlug(orgId).stream()
                .map(Organization::getSlug)
                .toList());
    orgs.add(orgId);
    orgs.add(WEXL_INTERNAL);
    var notifications =
        notificationRepository.getStudentNotificationsByIdAndOrgSlug(
            orgs, studentId, fromDate.toString(), appreciationMsg, category, limit);

    if (notifications.isEmpty()) {
      return Collections.emptyList();
    }

    return getStudentNotificationResponses(notifications, studentId);
  }

  public List<NotificationDto.StudentNotificationResponse> getStudentNotificationResponses(
      List<Notification> notifications, long studentId) {
    return notifications.stream()
        .map(
            notification -> {
              var studentNotification =
                  notification.getStudentNotifications().stream()
                      .filter(sn -> sn.getStudent().getId() == studentId)
                      .findFirst();
              return NotificationDto.StudentNotificationResponse.builder()
                  .notificationId(notification.getId())
                  .title(notification.getTitle())
                  .message(notification.getMessage())
                  .attributes(
                      studentNotification.map(StudentNotification::getTestAttributes).orElse(null))
                  .teacherName(
                      userService.getNameByUserInfo(
                          Objects.nonNull(notification.getCreatedBy())
                              ? notification.getCreatedBy().getUserInfo()
                              : notification.getUser()))
                  .type(notification.getNotificationType().name())
                  .attachment(convertAttachmentToS3Link(notification.getAttachments()))
                  .link(notification.getLink())
                  .messageTemplateCategory(
                      notification.getCategoryId() != null
                          ? messageTemplateService.getMessageTemplateCategory(
                              notification.getCreatedBy().getUserInfo().getOrganization(),
                              notification.getCategoryId())
                          : null)
                  .createdAt(convertIso8601ToEpoch(notification.getCreatedAt().toLocalDateTime()))
                  .fromDate(
                      notification.getFromDate() != null
                          ? convertIso8601ToEpoch(notification.getFromDate().toLocalDateTime())
                          : null)
                  .toDate(
                      notification.getToDate() != null
                          ? convertIso8601ToEpoch(notification.getToDate().toLocalDateTime())
                          : null)
                  .build();
            })
        .toList();
  }

  public List<NotificationDto.StudentNotificationResponse> getStudentNotificationsfromTeacher(
      String orgSlug,
      Long fromDateInEpoch,
      String studentAuthId,
      String appreciationMsg,
      String academicYear,
      Long limit) {
    User user = guardianService.validateUser(studentAuthId);
    var student = studentAuthService.validateStudentByUser(user);
    Long userId;
    if (academicYear != null) {
      var academicData = contentService.getAcademicYearBySlug(orgSlug, academicYear);
      var assetName = academicData.getAssetName();

      var yearParts = assetName.split("-");
      var endYear = yearParts[1];

      var currentYear = LocalDate.now().getYear();

      if (endYear.equals(String.valueOf(currentYear))) {
        userId = student.getId();
      } else {
        userId = student.getPrevStudentId();
      }
    } else {
      userId = student.getId();
    }
    var orgs =
        new ArrayList<>(
            organizationRepository.getParentOrgByChildOrgSlug(orgSlug).stream()
                .map(Organization::getSlug)
                .toList());
    orgs.add(orgSlug);
    orgs.add(WEXL_INTERNAL);
    var notifications =
        notificationRepository.getStudentNotificationsByIdAndOrgSlug(
            orgs,
            userId,
            Objects.nonNull(fromDateInEpoch)
                ? dateTimeUtil.convertEpochToIso8601Legacy(fromDateInEpoch).toLocalDate().toString()
                : null,
            appreciationMsg,
            null,
            limit);
    if (notifications.isEmpty()) {
      return Collections.emptyList();
    }
    return buildStudentNotificationResponse(notifications);
  }

  private List<NotificationDto.StudentNotificationResponse> buildStudentNotificationResponse(
      List<Notification> notifications) {
    List<NotificationDto.StudentNotificationResponse> responses = new ArrayList<>();
    for (Notification notification : notifications) {
      responses.add(
          NotificationDto.StudentNotificationResponse.builder()
              .notificationId(notification.getId())
              .title(notification.getTitle())
              .message(notification.getMessage())
              .attributes(notification.getStudentNotifications().getFirst().getTestAttributes())
              .teacherName(userService.getNameByUserInfo(notification.getCreatedBy().getUserInfo()))
              .type(notification.getNotificationType().name())
              .attachment(convertAttachmentToS3Link(notification.getAttachments()))
              .messageTemplateCategory(
                  notification.getCategoryId() != null
                      ? messageTemplateService.getMessageTemplateCategory(
                          notification.getOrgSlug(), notification.getCategoryId())
                      : null)
              .link(notification.getLink())
              .createdAt(convertIso8601ToEpoch(notification.getCreatedAt().toLocalDateTime()))
              .build());
    }
    return responses;
  }

  public Notification getNotificationByIdAndOrgSlug(Long id, String orgSlug) {

    return notificationRepository
        .findByIdAndOrgSlug(id, orgSlug)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "error.InvalidNotification",
                    new String[] {orgSlug}));
  }

  public void deleteNotification(String orgSlug, String teacherAuthId, Long id) {

    Teacher teacher =
        userRepository.findByAuthUserIdAndOrganization(teacherAuthId, orgSlug).getTeacherInfo();
    var notifications = notificationRepository.findAllByCreatedByAndOrgSlug(teacher, orgSlug);

    var notification = getNotificationByIdAndOrgSlug(id, orgSlug);

    var notificationIds = notifications.stream().map(Notification::getId).toList();

    if (!notificationIds.contains(notification.getId())) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.InvalidNotification", new String[] {orgSlug});
    }
    notificationRepository.delete(notification);
  }

  public List<NotificationDto.TeacherNotificationResponse> getTeacherAppreciationNotifications(
      String orgSlug, String teacherAuthId, int limit, String appreciationMessage) {
    Teacher teacher =
        userRepository.findByAuthUserIdAndOrganization(teacherAuthId, orgSlug).getTeacherInfo();

    List<Notification> notifications =
        notificationRepository.getTeacherAppreciationNotificationsByIdAndOrgSlug(
            orgSlug, teacher.getId(), limit, appreciationMessage);
    notifications.addAll(
        notificationRepository.getTeacherAppreciationNotificationsToAdmin(
            teacher.getId(), orgSlug, appreciationMessage));

    return buildAppreciationResponse(notifications, limit);
  }

  public List<NotificationDto.TeacherNotificationResponse> getTeacherNotifications(
      String orgSlug, String teacherAuthId, Long category, int limit, Boolean isAppreciation) {

    Teacher teacher =
        userRepository.findByAuthUserIdAndOrganization(teacherAuthId, orgSlug).getTeacherInfo();
    List<Notification> notifications =
        notificationRepository.getTeacherNotificationsByIdAndOrgSlug(
            orgSlug, teacher.getId(), category, limit);
    notifications.addAll(
        notificationRepository.getTeacherNotificationsToAdmin(
            teacher.getId(), orgSlug, category, limit));
    if (userRoleHelper.isManager(teacher.getUserInfo())) {
      notifications.addAll(
          notificationRepository.findByByTeacherIdAndCategory(teacher.getId(), category));
    }
    if (notifications.isEmpty()) {
      return Collections.emptyList();
    }
    notifications =
        notifications.stream()
            .filter(
                notification ->
                    notification.getNotificationType() != NotificationType.LEAVE_APPROVED
                        && notification.getNotificationType() != NotificationType.LEAVE_DISAPPROVED
                        && notification.getNotificationType()
                            != NotificationType.APPOINTMENT_APPROVED
                        && notification.getNotificationType()
                            != NotificationType.APPOINTMENT_DISAPPROVED
                        && notification.getNotificationType() != NotificationType.STAFF_APPOINTMENT)
            .toList();
    if (Boolean.TRUE.equals(isAppreciation)) {
      return buildAppreciationResponse(notifications, limit);
    }
    return buildNotificationResponse(notifications, limit);
  }

  private List<NotificationDto.TeacherNotificationResponse> buildAppreciationResponse(
      List<Notification> notifications, int limit) {
    return notifications.stream()
        .map(
            notification -> {
              var sections =
                  notification.getSectionNotifications().stream()
                      .map(SectionNotification::getSection)
                      .toList();
              return NotificationDto.TeacherNotificationResponse.builder()
                  .notificationId(notification.getId())
                  .title(notification.getTitle())
                  .attachment(convertAttachmentToS3Link(notification.getAttachments()))
                  .link(notification.getLink())
                  .type(notification.getNotificationType().name())
                  .message(notification.getMessage())
                  .messageTemplateCategory(
                      notification.getCategoryId() != null
                          ? messageTemplateService.getMessageTemplateCategory(
                              notification.getOrgSlug(), notification.getCategoryId())
                          : null)
                  .classroomName(
                      notification.getClassroomNotifications().stream()
                          .map(ClassroomNotification::getClassroom)
                          .map(Classroom::getName)
                          .toList())
                  .sectionIds(sections.stream().map(Section::getUuid).map(UUID::toString).toList())
                  .sectionName(sections.stream().map(Section::getName).toList())
                  .teacherNames(
                      userService.getNameByUserInfo(notification.getCreatedBy().getUserInfo()))
                  .createdAt(convertIso8601ToEpoch(notification.getCreatedAt().toLocalDateTime()))
                  .studentIds(
                      notification.getStudentNotifications().stream()
                          .map(StudentNotification::getStudent)
                          .map(Student::getId)
                          .toList())
                  .build();
            })
        .sorted(
            Comparator.comparing(NotificationDto.TeacherNotificationResponse::createdAt).reversed())
        .distinct()
        .limit(limit)
        .toList();
  }

  public List<NotificationDto.TeacherNotificationResponse> buildNotificationResponse(
      List<Notification> notifications, int limit) {
    return notifications.stream()
        .distinct()
        .map(
            notification -> {
              var sections =
                  notification.getSectionNotifications().stream()
                      .map(SectionNotification::getSection)
                      .filter(Objects::nonNull)
                      .toList();
              return NotificationDto.TeacherNotificationResponse.builder()
                  .notificationId(notification.getId())
                  .title(notification.getTitle())
                  .attachment(convertAttachmentToS3Link(notification.getAttachments()))
                  .link(notification.getLink())
                  .type(notification.getNotificationType().name())
                  .message(notification.getMessage())
                  .classroomName(
                      notification.getClassroomNotifications().stream()
                          .map(ClassroomNotification::getClassroom)
                          .map(Classroom::getName)
                          .toList())
                  .sectionIds(sections.stream().map(Section::getUuid).map(UUID::toString).toList())
                  .sectionName(sections.stream().map(Section::getName).toList())
                  .gradeSlug(notification.getGradeSlug())
                  .teacherNames(
                      notification.getCreatedBy() != null
                          ? userService.getNameByUserInfo(notification.getCreatedBy().getUserInfo())
                          : userService.getNameByUserInfo(notification.getUser()))
                  .createdAt(convertIso8601ToEpoch(notification.getCreatedAt().toLocalDateTime()))
                  .feature(notification.getFeature())
                  .colour(getCommunicationFeatureColour(notification.getFeature()))
                  .messageTemplateCategory(
                      notification.getCategoryId() != null
                          ? buildMessageTemplate(
                              notification.getOrgSlug(), notification.getCategoryId())
                          : null)
                  .fromDate(
                      notification.getFromDate() != null
                          ? convertIso8601ToEpoch(notification.getFromDate().toLocalDateTime())
                          : null)
                  .toDate(
                      notification.getToDate() != null
                          ? convertIso8601ToEpoch(notification.getToDate().toLocalDateTime())
                          : null)
                  .build();
            })
        .sorted(
            Comparator.comparing(NotificationDto.TeacherNotificationResponse::createdAt).reversed())
        .distinct()
        .limit(limit)
        .toList();
  }

  private String getCommunicationFeatureColour(CommunicationFeature feature) {
    if (feature == null) {
      return null;
    }
    return switch (feature) {
      case CIRCULAR -> "Blue";
      case HOMEWORK -> "Green";
      case HAPPENING -> "Yellow";
      case HOLIDAY -> "Red";
      case LEAVE -> "Purple";
      case EMAIL -> "Light Blue";
      case MESSAGE -> "Orange";
      case APPOINTMENT_REQUEST -> "Grey";
      default -> null;
    };
  }

  private MessageTemplateCategory buildMessageTemplate(String orgSlug, Long categoryId) {
    var messageTemplateCategory =
        messageTemplateService.getMessageTemplateCategory(orgSlug, categoryId);
    return MessageTemplateCategory.builder()
        .category(messageTemplateCategory.getCategory())
        .id(messageTemplateCategory.getId())
        .type(messageTemplateCategory.getType())
        .orgSlug(messageTemplateCategory.getOrgSlug())
        .category(messageTemplateCategory.getCategory())
        .build();
  }

  public List<String> convertAttachmentToS3Link(List<String> attachments) {
    if (Objects.nonNull(attachments)) {
      return attachments.stream().map(storageService::generatePreSignedUrlForFetch).toList();
    }
    return Collections.emptyList();
  }

  public NotificationDto.NotificationAttributes getNotificationById(
      String orgSlug, Long notificationId) {
    Notification notification;
    notification = getNotificationByIdAndOrgSlug(notificationId, orgSlug);
    User userInfo = notification.getCreatedBy().getUserInfo();
    List<NotificationDto.NotificationByIdResponse> notification1 =
        notification.getStudentNotifications().stream()
            .map(
                studentNotification -> {
                  Student student = studentNotification.getStudent();
                  User user = student.getUserInfo();
                  Section section = student.getSection();
                  return NotificationDto.NotificationByIdResponse.builder()
                      .userId(user.getAuthUserId())
                      .studentName(user.getFirstName() + " " + user.getLastName())
                      .smsStatus(studentNotification.getSmsStatus())
                      .emailStatus(studentNotification.getEmailStatus())
                      .whatsAppStatus(studentNotification.getWhatsappStatus())
                      .gradeName(section.getGradeName())
                      .gradeSlug(section.getGradeSlug())
                      .sectionName(section.getName())
                      .sectionUuid(section.getUuid().toString())
                      .build();
                })
            .toList();

    return NotificationDto.NotificationAttributes.builder()
        .title(notification.getTitle())
        .teacherName(userInfo.getFirstName() + " " + userInfo.getLastName())
        .notificationStudents(notification1)
        .build();
  }

  @Transactional
  public void editNotificationByTeacher(
      String orgSlug,
      NotificationDto.NotificationRequest notificationRequest,
      String teacherAuthId,
      Long notificationId) {

    Notification notification = getNotificationByIdAndOrgSlug(notificationId, orgSlug);
    sectionNotificationRepository.deleteAll(notification.getSectionNotifications());
    studentNotificationRepository.deleteAll(notification.getStudentNotifications());
    notification.getStudentNotifications().clear();
    notification.getClassroomNotifications().clear();
    notification.getSectionNotifications().clear();
    buildEditNotification(notificationRequest, notification);

    saveNotification(notification, notificationRequest, orgSlug, false);
  }

  private void buildEditNotification(
      NotificationDto.NotificationRequest notificationRequest, Notification notification) {
    notification.setTitle(notificationRequest.title());
    notification.setNotificationType(
        NotificationType.valueOf(notificationRequest.notificationType().name()));
    notification.setAttachments(notificationRequest.attachment());
    notification.setLink(notificationRequest.link());
    notification.setGradeSlug(notificationRequest.gradeSlug());
    notification.setMessage(notificationRequest.message());
  }

  public void buildTeacherNotification(
      List<Teacher> teachers, Notification notification, String orgSlug) {

    List<TeacherNotification> teacherNotifications = new ArrayList<>();
    teachers.forEach(
        teacher ->
            teacherNotifications.add(
                TeacherNotification.builder()
                    .notification(notification)
                    .teacher(teacher)
                    .orgSlug(orgSlug)
                    .build()));
    notification.setTeacherNotifications(teacherNotifications);
  }

  public void createNotificationByGrade(
      String orgSlug,
      NotificationDto.NotificationRequest notificationRequest,
      String teacherAuthId) {
    for (String gradeSlug : notificationRequest.gradeSlugs()) {
      var newNotificationRequest =
          NotificationDto.NotificationRequest.builder()
              .notificationType(notificationRequest.notificationType())
              .attachment(notificationRequest.attachment())
              .message(notificationRequest.message())
              .fromDate(notificationRequest.fromDate())
              .toDate(notificationRequest.toDate())
              .feature(notificationRequest.feature())
              .gradeSlug(gradeSlug)
              .categoryId(notificationRequest.categoryId())
              .classroomIds(notificationRequest.classroomIds())
              .studentIds(notificationRequest.studentIds())
              .link(notificationRequest.link())
              .title(notificationRequest.title())
              .messageTemplateId(notificationRequest.messageTemplateId())
              .sectionUuids(notificationRequest.sectionUuids())
              .build();

      createNotificationByTeacher(orgSlug, newNotificationRequest, teacherAuthId, false);
    }
  }

  public void createNotificationBySection(
      String orgSlug,
      NotificationDto.NotificationRequest notificationRequest,
      String teacherAuthId) {

    for (String sectionUuid : notificationRequest.sectionUuids()) {
      var newNotificationRequest =
          NotificationDto.NotificationRequest.builder()
              .notificationType(notificationRequest.notificationType())
              .attachment(notificationRequest.attachment())
              .message(notificationRequest.message())
              .fromDate(notificationRequest.fromDate())
              .toDate(notificationRequest.toDate())
              .feature(notificationRequest.feature())
              .gradeSlug(notificationRequest.gradeSlug())
              .categoryId(notificationRequest.categoryId())
              .classroomIds(notificationRequest.classroomIds())
              .studentIds(notificationRequest.studentIds())
              .link(notificationRequest.link())
              .title(notificationRequest.title())
              .messageTemplateId(notificationRequest.messageTemplateId())
              .sectionUuids(Collections.singletonList(sectionUuid))
              .build();

      createNotificationByTeacher(orgSlug, newNotificationRequest, teacherAuthId, false);
    }
  }

  public List<NotificationDto.NotificationLogResponse> getNotificationLogById(Long notificationId) {
    List<NotificationDto.NotificationLogResponse> response = new ArrayList<>();
    var notificationLogs = notificationLogRepository.findByNotificationId(notificationId);
    for (NotificationLog notificationLog : notificationLogs) {
      var student = studentService.getStudentById(notificationLog.getStudentId());
      response.add(
          NotificationDto.NotificationLogResponse.builder()
              .notificationId(notificationLog.getNotificationId())
              .studentId(notificationLog.getStudentId())
              .studentName(userService.getNameByUserInfo(student.getUserInfo()))
              .mobileNumber(notificationLog.getMobileNumber())
              .smsStatus(notificationLog.getSmsStatus())
              .whatsAppStatus(notificationLog.getWhatsAppStatus())
              .emailStatus(notificationLog.getEmailStatus())
              .rejected(notificationLog.getRejected())
              .rejectionReason(notificationLog.getRejectionReason())
              .build());
    }
    return response;
  }

  public void createNotificationByOrganization(
      NotificationRequest notificationRequest, String teacherAuthId) {
    for (String orgSlug : notificationRequest.orgSlugs()) {
      createNotificationByTeacher(orgSlug, notificationRequest, teacherAuthId, false);
    }
  }

  public List<GenericMetricResponse> getAllBranchNotificationCount(String orgSlug) {
    Organization organization = organizationRepository.findBySlug(orgSlug);
    List<Organization> childOrgs =
        organizationRepository.getAllChildOrgsByParentId(organization.getId());
    List<GenericMetricResponse> notificationsCount = new ArrayList<>();
    for (var childOrg : childOrgs) {
      Long countOfNotification =
          notificationRepository.getCountOfNotificationsByOrgSlug(childOrg.getSlug());
      Map<String, Object> summary = new HashMap<>();
      summary.put("org_name", childOrg.getName());
      summary.put("notification_count", countOfNotification);
      summary.put("org_slug", childOrg.getSlug());
      GenericMetricResponse metricResponse =
          GenericMetricResponse.builder().summary(summary).build();
      notificationsCount.add(metricResponse);
    }

    return notificationsCount;
  }

  public List<GenericMetricResponse> getNotificationCountByGrade(String orgSlug) {
    var organization = organizationRepository.findBySlug(orgSlug);
    List<String> gradeSlugs = getGradeSlugs();
    List<Map<String, Object>> notificationCountList = new ArrayList<>();
    for (String gradeSlug : gradeSlugs) {
      Grade grade = contentService.getGradeBySlug(gradeSlug);
      Long countOfNotificationsByGradeSlug =
          notificationRepository.getCountOfNotificationsByGradeSlug(orgSlug, gradeSlug);
      Map<String, Object> gradeCount = new HashMap<>();
      gradeCount.put("grade_name", grade.getName());
      gradeCount.put("grade_slug", grade.getSlug());
      gradeCount.put("count", countOfNotificationsByGradeSlug);
      notificationCountList.add(gradeCount);
    }
    Map<String, Object> summary = new HashMap<>();
    summary.put("organization_name", organization.getName());
    summary.put("notification_count", notificationCountList);
    return Collections.singletonList(GenericMetricResponse.builder().summary(summary).build());
  }

  public List<String> getGradeSlugs() {
    return Arrays.asList(
        "lkg", "ukg", "nur", "i", "ii", "iii", "iv", "v", "vi", "vii", "viii", "ix", "x", "xi",
        "xii");
  }

  public List<GenericMetricResponse> getNotificationByCategories(String orgSlug, String gradeSlug) {
    List<Map<String, Object>> notificationCountList = new ArrayList<>();
    List<MessageTemplateCategory> messageTemplateCategoryList =
        messageTemplateCategoryRepository.findAllByOrgSlug(orgSlug);
    for (MessageTemplateCategory messageTemplateCategory : messageTemplateCategoryList) {
      long countOfNotifications1 = 0;
      long countOfNotifications2 = 0;
      countOfNotifications1 =
          notificationRepository.getNotificationsCountByCategory(
              orgSlug, gradeSlug, messageTemplateCategory.getId());
      List<Long> messageTemplateIds =
          messageTemplateRepository.findByMessageTemplateCategory(messageTemplateCategory).stream()
              .map(MessageTemplate::getId)
              .toList();
      if (messageTemplateIds != null && !messageTemplateIds.isEmpty()) {
        countOfNotifications2 =
            notificationRepository.getNotificationsCountByTemplates(
                orgSlug, gradeSlug, messageTemplateIds);
      }
      Map<String, Object> typeCount = new HashMap<>();
      typeCount.put("type", messageTemplateCategory.getCategory());
      typeCount.put("count", Long.valueOf(countOfNotifications1 + countOfNotifications2));
      notificationCountList.add(typeCount);
    }

    Organization organization = organizationRepository.findBySlug(orgSlug);
    Map<String, Object> summary = new HashMap<>();
    summary.put("organization_name", organization.getName());
    summary.put("notification_count", notificationCountList);
    return Collections.singletonList(GenericMetricResponse.builder().summary(summary).build());
  }

  public List<NotificationDto.NotificationResponse> getNotificationsByFeatures(
      String orgSlug,
      String studentAuthId,
      Long fromDate,
      Long toDate,
      List<CommunicationFeature> features) {
    var student = validationUtils.validateStudentByAuthId(studentAuthId, orgSlug);
    var communicationReponses =
        notificationRepository.getNotificationsByFeaturesAndStudent(
            orgSlug,
            student.getId(),
            dateTimeUtil.convertEpochToIso8601(fromDate).toLocalDate().toString(),
            dateTimeUtil.convertEpochToIso8601(fromDate).toLocalDate().toString(),
            features.stream().map(CommunicationFeature::toString).toList());
    return groupTeacherNotificationsByDate(buildNotificationResponse(communicationReponses, 100));
  }

  public List<NotificationDto.NotificationResponse> getTeacherNotificationsByFeatures(
      String orgSlug,
      String teacherAuthId,
      Long fromDate,
      Long toDate,
      List<CommunicationFeature> features) {

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    var fromDateConverted =
        formatter.format(dateTimeUtil.convertEpochToIso8601(fromDate).toLocalDate());
    var toDateConverted =
        formatter.format(dateTimeUtil.convertEpochToIso8601(toDate).toLocalDate());
    var teacher = teacherService.getTeacherByAuthId(teacherAuthId);
    Long teacherId = teacher.getId();
    if (AuthUtil.isOrgAdmin(teacher.getUserInfo())) {
      teacherId = null;
    }
    var communicationResponses =
        notificationRepository.getNotificationsByFeaturesAndTeacher(
            orgSlug,
            teacherId,
            fromDateConverted,
            toDateConverted,
            features.stream().map(CommunicationFeature::toString).toList());
    return groupTeacherNotificationsByDate(buildNotificationResponse(communicationResponses, 100));
  }

  private List<NotificationDto.NotificationResponse> groupTeacherNotificationsByDate(
      List<NotificationDto.TeacherNotificationResponse> notifications) {

    Map<Long, List<NotificationDto.TeacherNotificationResponse>> groupedByDate =
        notifications.stream()
            .collect(
                Collectors.groupingBy(
                    n -> {
                      LocalDate date =
                          Instant.ofEpochMilli(n.createdAt())
                              .atZone(ZoneId.systemDefault())
                              .toLocalDate();
                      return date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    }));

    return groupedByDate.entrySet().stream()
        .map(
            entry ->
                NotificationDto.NotificationResponse.builder()
                    .date(entry.getKey())
                    .notifications(entry.getValue())
                    .build())
        .sorted(Comparator.comparing(NotificationDto.NotificationResponse::date))
        .toList();
  }

  public void createNotificationByStudent(String orgSlug, NotificationRequest notificationRequest) {
    var notification =
        buildUserNotification(notificationRequest, orgSlug, notificationRequest.userAuthId());
    List<Teacher> teachers = teacherRepository.findAllById(notificationRequest.teacherIds());
    buildTeacherNotification(teachers, notification, orgSlug);
    if (Objects.nonNull(notificationRequest.staffIds())
        && !notificationRequest.staffIds().isEmpty()) {
      List<Staff> staffs = staffRepository.findAllById(notificationRequest.staffIds());
      buildStaffNotification(orgSlug, staffs, notification);
    }
    notificationRepository.save(notification);
  }

  private void buildStaffNotification(
      String orgSlug, List<Staff> staffs, Notification notification) {
    List<StaffNotification> staffNotifications = new ArrayList<>();
    staffs.forEach(
        staff ->
            staffNotifications.add(
                StaffNotification.builder()
                    .notification(notification)
                    .staff(staff)
                    .orgSlug(orgSlug)
                    .build()));
    notification.setStaffNotifications(staffNotifications);
  }
}
