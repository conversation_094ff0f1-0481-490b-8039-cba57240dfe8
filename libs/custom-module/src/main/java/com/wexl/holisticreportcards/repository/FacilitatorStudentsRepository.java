package com.wexl.holisticreportcards.repository;

import com.wexl.holisticreportcards.model.Facilitator;
import com.wexl.holisticreportcards.model.FacilitatorStudents;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface FacilitatorStudentsRepository extends JpaRepository<FacilitatorStudents, Long> {

  List<FacilitatorStudents> findByStudentId(Long studentId);

  Optional<FacilitatorStudents> findByStudentIdAndFacilitator(
      long studentId, Facilitator facilitator);

  List<FacilitatorStudents> findByStudentIdAndFacilitatorIn(
      long studentId, List<Facilitator> facilitator);
}
