package com.wexl.holisticreportcards.repository;

import com.wexl.holisticreportcards.model.FacilitatorObservations;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface FacilitatorObservationsRepository
    extends JpaRepository<FacilitatorObservations, Long> {
  Optional<FacilitatorObservations> findByStudentId(long studentId);
}
