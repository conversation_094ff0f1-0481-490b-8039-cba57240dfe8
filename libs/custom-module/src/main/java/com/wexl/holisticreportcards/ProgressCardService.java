package com.wexl.holisticreportcards;

import static com.wexl.retail.commons.util.DateTimeUtil.parseDateToEpochMilli;

import com.wexl.holisticreportcards.dto.ProgressCardDto;
import com.wexl.holisticreportcards.model.*;
import com.wexl.holisticreportcards.repository.*;
import com.wexl.retail.curriculum.service.OrgSettingsService;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.guardian.repository.GuardianRepository;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.attributes.dto.StudentAttributeDto;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.subjects.repository.SubjectsMetaDataRepository;
import com.wexl.retail.util.ValidationUtils;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ProgressCardService {

  private final OrgSettingsService orgSettingsService;
  private final StudentAuthService studentAuthService;
  private final ReportCardService reportCardService;
  private final StorageService storageService;
  private final ProgressCardRepository progressCardRepository;
  private final CompetencyRepository competencyRepository;
  private final SelfAssessmentRepository selfAssessmentRepository;
  private final PeerAssessmentRepository peerAssessmentRepository;
  private final CompetenciesStudentRepository competenciesStudentRepository;
  private final SelfAssessmentStudentRepository selfAssessmentStudentRepository;
  private final PeerAssessmentStudentRepository peerAssessmentStudentRepository;
  private final ParentsFeedBackRepository parentsFeedBackRepository;
  private final ParentsFeedBackStudentsRepository parentsFeedBackStudentsRepository;
  private final StudentAttributeService studentAttributeService;
  private final GuardianRepository guardianRepository;
  private final ValidationUtils validationUtils;
  private final FacilitatorRepository facilitatorRepository;
  private final FacilitatorStudentsRepository facilitatorStudentsRepository;
  private final SummarySheetRepository summarySheetRepository;
  private final SummarySheetStudentsRepository summarySheetStudentsRepository;
  private final ChildBetterRepository childBetterRepository;
  private final ChildBetterStudentsRepository childBetterStudentRepository;
  private final SubjectsMetaDataRepository subjectsMetaDataRepository;
  private final FacilitatorObservationsRepository facilitatorObservationsRepository;

  public ProgressCardDto.ProgressCardResponse saveProgressCard(
      String orgSlug, ProgressCardDto.Request request) {
    orgSettingsService.validateOrganizaiton(orgSlug);
    var student = studentAuthService.validateStudentById(request.studentId());
    ProgressCard progressCard = getOrCreateProgressCard(request, orgSlug, student);
    Long savedProgressCardId;
    savedProgressCardId = progressCardRepository.save(progressCard).getId();
    return ProgressCardDto.ProgressCardResponse.builder()
        .PallaviPrimaryProgressCardId(savedProgressCardId)
        .build();
  }

  private ProgressCard getOrCreateProgressCard(
      ProgressCardDto.Request request, String orgSlug, Student student) {
    if (request.PallaviPrimaryProgressCardId() != null) {
      return progressCardRepository
          .findById(request.PallaviPrimaryProgressCardId())
          .map(existingCard -> updateProgressCardFields(existingCard, request, orgSlug, student))
          .orElseGet(() -> findByStudentOrBuildNew(request, orgSlug, student));
    }

    return findByStudentOrBuildNew(request, orgSlug, student);
  }

  private ProgressCard findByStudentOrBuildNew(
      ProgressCardDto.Request request, String orgSlug, Student student) {
    var existingCards = progressCardRepository.findByStudentId(student.getId());
    if (existingCards != null) {
      return updateProgressCardFields(existingCards, request, orgSlug, student);
    }
    return buildNewProgressCard(request, orgSlug, student);
  }

  private ProgressCard updateProgressCardFields(
      ProgressCard existingCard, ProgressCardDto.Request request, String orgSlug, Student student) {
    saveCompetency(request, orgSlug, student);
    saveSelfAssessment(request, orgSlug, student);
    savePeerAssessment(request, orgSlug, student);
    saveParentsFeedBack(request, orgSlug, student);
    saveFacilitator(request, orgSlug, student);
    saveSummarySheet(request, orgSlug, student);
    saveChildBetter(request, orgSlug, student);
    saveAddress(request, orgSlug, student);
    saveMotherPhoneNumber(request, student);
    saveHouse(request, orgSlug, student);
    existingCard.setStudentId(request.studentId());
    existingCard.setThingsILike(
        getPreferredValue(request.thingsILike(), existingCard.getThingsILike()));
    existingCard.setILiveIn(getPreferredValue(request.iLiveIn(), existingCard.getILiveIn()));
    existingCard.setMyFriendsAre(
        getPreferredValue(request.myFriendsAre(), existingCard.getMyFriendsAre()));
    existingCard.setMyFavouriteColoursAre(
        getPreferredValue(
            request.myFavouriteColoursAre(), existingCard.getMyFavouriteColoursAre()));
    existingCard.setMyFavouriteFoods(
        getPreferredValue(request.myFavouriteFoods(), existingCard.getMyFavouriteFoods()));
    existingCard.setMyFavouriteGames(
        getPreferredValue(request.myFavouriteGames(), existingCard.getMyFavouriteGames()));
    existingCard.setMyFavouriteAnimals(
        getPreferredValue(request.myFavouriteAnimals(), existingCard.getMyFavouriteAnimals()));
    existingCard.setAGlimpseOfMyFamily(
        getPreferredValue(request.aGlimpseOfMyFamily(), existingCard.getAGlimpseOfMyFamily()));
    existingCard.setAGlimpseOfMySelf(
        getPreferredValue(request.aGlimpseOfMySelf(), existingCard.getAGlimpseOfMySelf()));
    existingCard.setLearnersPortFolio(
        getPreferredValue(request.learnersPortFolio(), existingCard.getLearnersPortFolio()));
    existingCard.setTerm2Height(
        getPreferredValue(request.term2Height(), existingCard.getTerm2Height()));
    existingCard.setTerm1Height(
        getPreferredValue(request.term1Height(), existingCard.getTerm1Height()));
    existingCard.setTerm1Weight(
        getPreferredValue(request.term1Weight(), existingCard.getTerm1Weight()));
    existingCard.setTerm2Weight(
        getPreferredValue(request.term2Weight(), existingCard.getTerm2Weight()));
    existingCard.setAim(getPreferredValue(request.aim(), existingCard.getAim()));
    existingCard.setFlower(getPreferredValue(request.flower(), existingCard.getFlower()));
    existingCard.setSubject(getPreferredValue(request.subject(), existingCard.getSubject()));
    existingCard.setAge(request.age());
    existingCard.setBloodGroup(
        getPreferredValue(request.bloodGroup(), existingCard.getBloodGroup()));
    existingCard.setDental(getPreferredValue(request.dental(), existingCard.getDental()));
    existingCard.setEyesightL(getPreferredValue(request.eyesightL(), existingCard.getEyesightL()));
    existingCard.setEyesightR(getPreferredValue(request.eyesightR(), existingCard.getEyesightR()));
    existingCard.setTerm1Remarks(request.term1Remarks());
    existingCard.setTerm2Remarks(request.term2Remarks());
    existingCard.setInterestedActivities(request.interestedActivities());
    existingCard.setChildSupport(request.childSupport());
    existingCard.setParentFacilitator(request.parentFacilitator());
    return existingCard;
  }

  private void saveMotherPhoneNumber(ProgressCardDto.Request request, Student student) {
    var guardians = student.getGuardians();
    var motherDetails =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.MOTHER)).findAny();
    if (motherDetails.isPresent()
        && request.motherPhoneNo() != null
        && !request.motherPhoneNo().isEmpty()) {
      motherDetails.get().setMobileNumber(request.motherPhoneNo());
      guardianRepository.save(motherDetails.get());
    }
  }

  private void saveFacilitator(ProgressCardDto.Request request, String orgSlug, Student student) {
    saveOrUpdateFacilitatorStudents(request.facilitator(), student);
    saveOrUpdateFacilitatorObservations(student, request.facilitator());
  }

  private void saveOrUpdateFacilitatorObservations(
      Student student, ProgressCardDto.Facilitator request) {
    var studentFacilitatorObservations =
        facilitatorObservationsRepository.findByStudentId(student.getId());
    if (studentFacilitatorObservations.isEmpty()) {
      buildFacilitatorObservations(student, request, new FacilitatorObservations());
    } else {
      buildFacilitatorObservations(student, request, studentFacilitatorObservations.get());
    }
  }

  private void buildFacilitatorObservations(
      Student student,
      ProgressCardDto.Facilitator request,
      FacilitatorObservations facilitatorObservations) {
    facilitatorObservations.setStudentId(student.getId());
    facilitatorObservations.setAchievements(request.achievement());
    facilitatorObservations.setAreaOfStrength(request.areaOfStrength());
    facilitatorObservations.setClassFacilitatorRemarks(request.classFacilitatorRemarks());
    facilitatorObservations.setStudentProgress(request.studentProgress());
    facilitatorObservations.setParticipation(request.participation());
    facilitatorObservations.setBarrierOfSuccess(request.barrierOfSuccess());
    facilitatorObservations.setPrincipalRemarks(request.principalRemarks());
    facilitatorObservationsRepository.save(facilitatorObservations);
  }

  private void saveOrUpdateFacilitatorStudents(
      ProgressCardDto.Facilitator request, Student student) {

    List<FacilitatorStudents> facilitatorStudents = new ArrayList<>();

    request
        .skills()
        .forEach(
            skill -> {
              if (skill.facilitatorDetails() != null) {
                skill.facilitatorDetails().stream()
                    .filter(Objects::nonNull)
                    .forEach(
                        facilitatorDetails -> {
                          var facilitator =
                              facilitatorRepository.findById(facilitatorDetails.id()).orElseThrow();
                          var studentFacilitator =
                              facilitatorStudentsRepository.findByStudentIdAndFacilitator(
                                  student.getId(), facilitator);
                          if (studentFacilitator.isPresent()) {
                            studentFacilitator.get().setTerm1(facilitatorDetails.term1());
                            studentFacilitator.get().setTerm2(facilitatorDetails.term2());
                            facilitatorStudents.add(studentFacilitator.get());
                          } else {
                            FacilitatorStudents facilitatorStudent = new FacilitatorStudents();
                            facilitatorStudent.setFacilitator(facilitator);
                            facilitatorStudent.setStudentId(student.getId());
                            facilitatorStudent.setTerm1(facilitatorDetails.term1());
                            facilitatorStudent.setTerm2(facilitatorDetails.term2());
                            facilitatorStudents.add(facilitatorStudent);
                          }
                        });
              }
            });
    facilitatorStudentsRepository.saveAll(facilitatorStudents);
  }

  private FacilitatorStudents buildFacilitatorStudentMapping(
      Facilitator facilitator,
      ProgressCardDto.FacilitatorDetails facilitatorDetails,
      Student student) {

    return FacilitatorStudents.builder()
        .facilitator(facilitator)
        .studentId(student.getId())
        .term1(facilitatorDetails.term1())
        .term2(facilitatorDetails.term2())
        .build();
  }

  private void saveAddress(ProgressCardDto.Request request, String orgSlug, Student student) {
    var user = student.getUserInfo();
    String authUserId = user.getAuthUserId();

    var studentAttributes = studentAttributeService.getStudentAttributes(authUserId, null);
    String existingAddress = studentAttributes.get("residential_address");
    String newAddress = request.address();
    if (!Objects.equals(existingAddress, newAddress) && newAddress != null) {
      Map<String, String> attributes = Map.of("residential_address", newAddress);
      var buildAttributes = StudentAttributeDto.Request.builder().attributes(attributes).build();
      studentAttributeService.saveStudentDefinitionAttributes(authUserId, orgSlug, buildAttributes);
    }
  }

  private void saveHouse(ProgressCardDto.Request request, String orgSlug, Student student) {
    var user = student.getUserInfo();
    String authUserId = user.getAuthUserId();

    var studentAttributes = studentAttributeService.getStudentAttributes(authUserId, null);
    String existingAddress = studentAttributes.get("house");
    String newHouse = request.house();
    if (!Objects.equals(existingAddress, newHouse) && newHouse != null) {
      Map<String, String> attributes = Map.of("house", newHouse);
      var buildAttributes = StudentAttributeDto.Request.builder().attributes(attributes).build();
      studentAttributeService.saveStudentDefinitionAttributes(authUserId, orgSlug, buildAttributes);
    }
  }

  private ProgressCard buildNewProgressCard(
      ProgressCardDto.Request request, String orgSlug, Student student) {
    saveCompetency(request, orgSlug, student);
    saveSelfAssessment(request, orgSlug, student);
    savePeerAssessment(request, orgSlug, student);
    saveFacilitator(request, orgSlug, student);
    saveSummarySheet(request, orgSlug, student);
    saveChildBetter(request, orgSlug, student);
    saveAddress(request, orgSlug, student);
    saveMotherPhoneNumber(request, student);
    return ProgressCard.builder()
        .thingsILike(request.thingsILike())
        .iLiveIn(request.iLiveIn())
        .myFriendsAre(request.myFriendsAre())
        .myFavouriteColoursAre(request.myFavouriteColoursAre())
        .myFavouriteFoods(request.myFavouriteFoods())
        .myFavouriteGames(request.myFavouriteGames())
        .myFavouriteAnimals(request.myFavouriteAnimals())
        .aGlimpseOfMyFamily(request.aGlimpseOfMyFamily())
        .aGlimpseOfMySelf(request.aGlimpseOfMySelf())
        .learnersPortFolio(request.learnersPortFolio())
        .aim(request.aim())
        .flower(request.flower())
        .subject(request.subject())
        .term1Height(request.term1Height())
        .term1Weight(request.term1Weight())
        .term2Height(request.term2Height())
        .term2Weight(request.term2Weight())
        .studentId(request.studentId())
        .age(request.age())
        .bloodGroup(request.bloodGroup())
        .dental(request.dental())
        .eyesightL(request.eyesightL())
        .eyesightR(request.eyesightR())
        .term1Remarks(request.term1Remarks())
        .term2Remarks(request.term2Remarks())
        .interestedActivities(request.interestedActivities())
        .childSupport(request.childSupport())
        .parentFacilitator(request.parentFacilitator())
        .build();
  }

  private void saveChildBetter(ProgressCardDto.Request request, String orgSlug, Student student) {
    var section = student.getSection();
    var childBetterList =
        childBetterRepository.findByOrgSlugAndGradeSlug(orgSlug, section.getGradeSlug());

    processChildBetterData(childBetterList, request.childBetter(), student, orgSlug);
  }

  private void processChildBetterData(
      List<ChildBetter> childBetterList,
      List<ProgressCardDto.ChildBetter> request,
      Student student,
      String orgSlug) {
    if (childBetterList.isEmpty()) {
      request.forEach(
          dto -> {
            ChildBetter newChildBetter = new ChildBetter();
            newChildBetter.setName(dto.name());
            newChildBetter.setOrgSlug(orgSlug);
            newChildBetter.setGradeSlug(student.getSection().getGradeSlug());

            ChildBetter savedChildBetter = childBetterRepository.save(newChildBetter);

            ChildBetterStudents newStudentMapping =
                ChildBetterStudents.builder()
                    .childBetter(savedChildBetter)
                    .studentId(student.getId())
                    .term1(dto.term1())
                    .term2(dto.term2())
                    .build();

            childBetterStudentRepository.save(newStudentMapping);
          });

    } else {
      childBetterList.forEach(
          existingChildBetter -> {
            request.stream()
                .filter(dto -> dto.id() != null && dto.id().equals(existingChildBetter.getId()))
                .findFirst()
                .ifPresent(
                    dto -> {
                      existingChildBetter.setName(dto.name());
                      existingChildBetter.setOrgSlug(orgSlug);
                      existingChildBetter.setGradeSlug(student.getSection().getGradeSlug());

                      ChildBetter updatedChildBetter =
                          childBetterRepository.save(existingChildBetter);

                      Optional<ChildBetterStudents> existingStudentMappingOpt =
                          updatedChildBetter.getStudents().stream()
                              .filter(mapping -> mapping.getStudentId().equals(student.getId()))
                              .findAny();

                      if (existingStudentMappingOpt.isPresent()) {
                        ChildBetterStudents mapping = existingStudentMappingOpt.get();
                        mapping.setChildBetter(updatedChildBetter);
                        mapping.setTerm1(dto.term1());
                        mapping.setTerm2(dto.term2());
                        childBetterStudentRepository.save(mapping);
                      } else {
                        ChildBetterStudents newMapping =
                            ChildBetterStudents.builder()
                                .childBetter(updatedChildBetter)
                                .studentId(student.getId())
                                .term1(dto.term1())
                                .term2(dto.term2())
                                .build();
                        childBetterStudentRepository.save(newMapping);
                      }
                    });
          });
    }
  }

  private void saveSummarySheet(ProgressCardDto.Request request, String orgSlug, Student student) {
    var section = student.getSection();
    var summarySheetList =
        summarySheetRepository.findByOrgSlugAndGradeSlug(orgSlug, section.getGradeSlug());

    processSummarySheetData(summarySheetList, request.summarySheet(), student, orgSlug);
  }

  private void processSummarySheetData(
      List<SummarySheet> summarySheetList,
      List<ProgressCardDto.SummarySheet> request,
      Student student,
      String orgSlug) {

    if (summarySheetList.isEmpty()) {
      request.forEach(
          dto -> {
            SummarySheet newSheet = new SummarySheet();
            newSheet.setName(dto.name());
            newSheet.setSkill(dto.skill());
            newSheet.setOrgSlug(orgSlug);
            newSheet.setGradeSlug(student.getSection().getGradeSlug());

            SummarySheet savedSheet = summarySheetRepository.save(newSheet);

            SummarySheetStudents newStudentMapping =
                SummarySheetStudents.builder()
                    .summarySheet(savedSheet)
                    .studentId(student.getId())
                    .term1(dto.term1())
                    .term2(dto.term2())
                    .build();

            summarySheetStudentsRepository.save(newStudentMapping);
          });

    } else {
      summarySheetList.forEach(
          existingSheet -> {
            request.stream()
                .filter(dto -> dto.id() != null && dto.id().equals(existingSheet.getId()))
                .findFirst()
                .ifPresent(
                    dto -> {
                      existingSheet.setName(dto.name());
                      existingSheet.setSkill(dto.skill());
                      existingSheet.setOrgSlug(orgSlug);
                      existingSheet.setGradeSlug(student.getSection().getGradeSlug());

                      SummarySheet updatedSheet = summarySheetRepository.save(existingSheet);

                      Optional<SummarySheetStudents> existingStudentMappingOpt =
                          updatedSheet.getStudents().stream()
                              .filter(mapping -> mapping.getStudentId().equals(student.getId()))
                              .findAny();

                      if (existingStudentMappingOpt.isPresent()) {
                        SummarySheetStudents mapping = existingStudentMappingOpt.get();
                        mapping.setSummarySheet(updatedSheet);
                        mapping.setTerm1(dto.term1());
                        mapping.setTerm2(dto.term2());
                        summarySheetStudentsRepository.save(mapping);
                      } else {
                        SummarySheetStudents newMapping =
                            SummarySheetStudents.builder()
                                .summarySheet(updatedSheet)
                                .studentId(student.getId())
                                .term1(dto.term1())
                                .term2(dto.term2())
                                .build();
                        summarySheetStudentsRepository.save(newMapping);
                      }
                    });
          });
    }
  }

  private void saveParentsFeedBack(
      ProgressCardDto.Request request, String orgSlug, Student student) {
    var section = student.getSection();
    var parentsFeedBackList =
        parentsFeedBackRepository.findByOrgSlugAndGradeSlug(orgSlug, section.getGradeSlug());

    processSaveParentsFeedBackData(parentsFeedBackList, request.parentsFeedbacks(), student);
  }

  private void processSaveParentsFeedBackData(
      List<ParentsFeedBack> parentsFeedBackList,
      List<ProgressCardDto.ParentsFeedback> parentsFeedbacks,
      Student student) {
    parentsFeedBackList.forEach(
        feedback -> {
          var studentDataOpt =
              feedback.getStudents().stream()
                  .filter(x -> x.getStudentId().equals(student.getId()))
                  .findAny();

          var assessmentOpt =
              parentsFeedbacks.stream().filter(x -> x.id().equals(feedback.getId())).findAny();

          if (assessmentOpt.isPresent()) {
            var assessment = assessmentOpt.get();
            if (studentDataOpt.isEmpty()) {
              parentsFeedBackStudentsRepository.save(
                  ParentsFeedBackStudents.builder()
                      .parentsFeedBack(feedback)
                      .studentId(student.getId())
                      .term1(assessment.term1())
                      .term2(assessment.term2())
                      .build());
            } else {
              var studentData = studentDataOpt.get();
              studentData.setTerm1(assessment.term1());
              studentData.setTerm2(assessment.term2());
              parentsFeedBackStudentsRepository.save(studentData);
            }
          }
        });
  }

  private void savePeerAssessment(
      ProgressCardDto.Request request, String orgSlug, Student student) {
    var section = student.getSection();
    var selfAssessmentData =
        peerAssessmentRepository.findByOrgSlugAndGradeSlug(orgSlug, section.getGradeSlug());

    processPeerAssessmentData(selfAssessmentData, request.peerAssessments(), student);
  }

  private void processPeerAssessmentData(
      List<PeerAssessments> peerAssessmentData,
      List<ProgressCardDto.PeerAssessment> peerAssessments,
      Student student) {
    if (peerAssessmentData.isEmpty()) {
      peerAssessments.forEach(
          dto -> {
            PeerAssessments newPeerAssessment = new PeerAssessments();
            newPeerAssessment.setName(dto.name());
            newPeerAssessment.setOrgSlug(student.getUserInfo().getOrganization());
            newPeerAssessment.setGradeSlug(student.getSection().getGradeSlug());
            newPeerAssessment.setDescriptionName(dto.descriptionName());

            PeerAssessments savedPeerAssessment = peerAssessmentRepository.save(newPeerAssessment);
            PeerAssessmentStudents studentMapping =
                PeerAssessmentStudents.builder()
                    .peerAssessments(savedPeerAssessment)
                    .studentId(student.getId())
                    .term1(dto.term1())
                    .term2(dto.term2())
                    .build();
            peerAssessmentStudentRepository.save(studentMapping);
          });
    } else {
      peerAssessmentData.forEach(
          feedback -> {
            var studentDataOpt =
                feedback.getStudents().stream()
                    .filter(x -> x.getStudentId().equals(student.getId()))
                    .findAny();

            var assessmentOpt =
                peerAssessments.stream().filter(x -> x.id().equals(feedback.getId())).findAny();

            if (assessmentOpt.isPresent()) {
              var assessment = assessmentOpt.get();
              feedback.setName(assessment.name());
              feedback.setDescriptionName(assessment.descriptionName());
              if (studentDataOpt.isEmpty()) {
                peerAssessmentStudentRepository.save(
                    PeerAssessmentStudents.builder()
                        .peerAssessments(feedback)
                        .studentId(student.getId())
                        .term1(assessment.term1())
                        .term2(assessment.term2())
                        .build());
              } else {
                var studentData = studentDataOpt.get();
                studentData.setPeerAssessments(feedback);
                studentData.setTerm1(assessment.term1());
                studentData.setTerm2(assessment.term2());
                peerAssessmentStudentRepository.save(studentData);
              }
              peerAssessmentRepository.save(feedback);
            }
          });
    }
  }

  private void saveSelfAssessment(
      ProgressCardDto.Request request, String orgSlug, Student student) {
    var section = student.getSection();
    var selfAssessmentData =
        selfAssessmentRepository.findByOrgSlugAndGradeSlug(orgSlug, section.getGradeSlug());

    processSelfAssessmentData(selfAssessmentData, request.selfAssessments(), student);
  }

  private void processSelfAssessmentData(
      List<SelfAssessment> competencyData,
      List<ProgressCardDto.SelfAssessment> feedBacks,
      Student student) {
    if (competencyData.isEmpty()) {
      feedBacks.forEach(
          dto -> {
            SelfAssessment newSelfAssessment = new SelfAssessment();
            newSelfAssessment.setName(dto.name());
            newSelfAssessment.setOrgSlug(student.getUserInfo().getOrganization());
            newSelfAssessment.setGradeSlug(student.getSection().getGradeSlug());
            newSelfAssessment.setDescriptionName(dto.descriptionName());

            SelfAssessment savedSelfAssessment = selfAssessmentRepository.save(newSelfAssessment);
            SelfAssessmentStudents studentMapping =
                SelfAssessmentStudents.builder()
                    .selfAssessment(savedSelfAssessment)
                    .studentId(student.getId())
                    .term1(dto.term1())
                    .term2(dto.term2())
                    .build();
            selfAssessmentStudentRepository.save(studentMapping);
          });
    } else {
      competencyData.forEach(
          feedback -> {
            var studentDataOpt =
                feedback.getStudents().stream()
                    .filter(x -> x.getStudentId().equals(student.getId()))
                    .findAny();

            var assessmentOpt =
                feedBacks.stream().filter(x -> x.id().equals(feedback.getId())).findAny();

            if (assessmentOpt.isPresent()) {
              var assessment = assessmentOpt.get();
              feedback.setName(assessment.name());
              feedback.setDescriptionName(assessment.descriptionName());
              if (studentDataOpt.isEmpty()) {
                selfAssessmentStudentRepository.save(
                    SelfAssessmentStudents.builder()
                        .selfAssessment(feedback)
                        .studentId(student.getId())
                        .term1(assessment.term1())
                        .term2(assessment.term2())
                        .build());
              } else {
                var studentData = studentDataOpt.get();
                studentData.setSelfAssessment(feedback);
                studentData.setTerm1(assessment.term1());
                studentData.setTerm2(assessment.term2());
                selfAssessmentStudentRepository.save(studentData);
              }
              selfAssessmentRepository.save(feedback);
            }
          });
    }
  }

  private void saveCompetency(ProgressCardDto.Request request, String orgSlug, Student student) {
    var section = student.getSection();
    var competencyData =
        competencyRepository.findByOrgSlugAndGradeSlug(orgSlug, section.getGradeSlug());
    processCompetencyData(competencyData, request.competenciesList(), student);
  }

  private void processCompetencyData(
      List<Competencies> competencyData,
      List<ProgressCardDto.Competencies> feedBacks,
      Student student) {

    competencyData.forEach(
        competency -> {
          Optional<CompetenciesStudents> studentDataOpt =
              CollectionUtils.isEmpty(competency.getStudents())
                  ? Optional.empty()
                  : competency.getStudents().stream()
                      .filter(x -> x.getStudentId().equals(student.getId()))
                      .findAny();

          var assessmentOpt =
              feedBacks.stream()
                  .filter(x -> x.subjectSlug().equals(competency.getSubjectSlug()))
                  .findAny();

          assessmentOpt.ifPresent(
              assessmentList -> {
                List<ProgressCardDto.Details> details =
                    assessmentList.skills().stream()
                        .map(ProgressCardDto.Skill::competencyDetails)
                        .flatMap(Collection::stream)
                        .toList();
                for (var assessment : details) {
                  if (Objects.equals(assessment.id(), competency.getId())) {
                    studentDataOpt.ifPresentOrElse(
                        studentData -> updateExistingStudentData(studentData, assessment),
                        () -> saveNewStudentData(competency, student, assessment));
                  }
                }
              });
        });
  }

  private void updateExistingStudentData(
      CompetenciesStudents studentData, ProgressCardDto.Details assessment) {
    studentData.setTerm1(assessment.term1());
    studentData.setTerm2(assessment.term2());
    competenciesStudentRepository.save(studentData);
  }

  private void saveNewStudentData(
      Competencies competency, Student student, ProgressCardDto.Details assessment) {
    competenciesStudentRepository.save(
        CompetenciesStudents.builder()
            .pallaviPrePrimaryCompetencies(competency)
            .studentId(student.getId())
            .term1(assessment.term1())
            .term2(assessment.term2())
            .build());
  }

  private String getPreferredValue(String requestValue, String dataValue) {
    return requestValue != null ? requestValue : dataValue;
  }

  public ProgressCardDto.Response getProgressCardById(String orgSlug, String studentAuthId) {
    var student = validationUtils.validateStudentByAuthId(studentAuthId, orgSlug);
    orgSettingsService.validateOrganizaiton(orgSlug);
    var studentData = progressCardRepository.findByStudentId(student.getId());
    return buildResponse(studentData, student.getId(), orgSlug);
  }

  private ProgressCardDto.Response buildResponse(
      ProgressCard studentData, Long studentId, String orgSlug) {
    var student = studentAuthService.validateStudentById(studentId);
    var section = student.getSection();
    Optional<StudentAttributeValueModel> house =
        reportCardService.getStudentAttributeValue(student, "house");
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    Optional<StudentAttributeValueModel> address =
        reportCardService.getStudentAttributeValue(student, "residential_address");
    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    var studentUser = student.getUserInfo();
    var guardians = studentAuthService.getGuardians(student);
    Teacher teacher =
        Objects.isNull(student.getSection().getClassTeacher())
            ? null
            : student.getSection().getClassTeacher();
    return ProgressCardDto.Response.builder()
        .PallaviPrimaryProgressCardId(studentData == null ? null : studentData.getId())
        .studentId(student.getId())
        .studentName(studentUser.getFirstName() + " " + studentUser.getLastName())
        .gradeName(section.getGradeName())
        .gradeSlug(section.getGradeSlug())
        .sectionName(section.getName())
        .address(address.map(StudentAttributeValueModel::getValue).orElse(null))
        .dateOfBirth(dateOfBirth.map(s -> parseDateToEpochMilli(s.getValue())).orElse(null))
        .house(house.map(StudentAttributeValueModel::getValue).orElse(null))
        .admissionNo(student.getRollNumber())
        .fatherName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .motherName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .fatherPhoneNo(student.getUserInfo().getMobileNumber())
        .motherPhoneNo(guardians.mothersMobileNumber())
        .classTeacher(
            teacher == null
                ? null
                : teacher.getUserInfo().getFirstName() + " " + teacher.getUserInfo().getLastName())
        .thingsILike(studentData == null ? null : studentData.getThingsILike())
        .iLiveIn(studentData == null ? null : studentData.getILiveIn())
        .myFriendsAre(studentData == null ? null : studentData.getMyFriendsAre())
        .myFavouriteColoursAre(studentData == null ? null : studentData.getMyFavouriteColoursAre())
        .myFavouriteFoods(studentData == null ? null : studentData.getMyFavouriteFoods())
        .aim(studentData == null ? null : studentData.getAim())
        .flower(studentData == null ? null : studentData.getFlower())
        .subject(studentData == null ? null : studentData.getSubject())
        .myFavouriteGames(studentData == null ? null : studentData.getMyFavouriteGames())
        .myFavouriteAnimals(studentData == null ? null : studentData.getMyFavouriteAnimals())
        .aGlimpseOfMyFamilyPath(
            Objects.isNull(studentData)
                ? null
                : Objects.isNull(studentData.getAGlimpseOfMyFamily())
                    ? null
                    : studentData.getAGlimpseOfMyFamily())
        .aGlimpseOfMyFamily(
            studentData == null ? null : fetchImage(studentData.getAGlimpseOfMyFamily()))
        .aGlimpseOfMySelfPath(
            Objects.isNull(studentData)
                ? null
                : Objects.isNull(studentData.getAGlimpseOfMySelf())
                    ? null
                    : studentData.getAGlimpseOfMySelf())
        .aGlimpseOfMySelf(
            studentData == null ? null : fetchImage(studentData.getAGlimpseOfMySelf()))
        .learnersPortFolioPath(
            Objects.isNull(studentData)
                ? null
                : Objects.isNull(studentData.getLearnersPortFolio())
                    ? null
                    : studentData.getLearnersPortFolio())
        .learnersPortFolio(
            studentData == null ? null : fetchImage(studentData.getLearnersPortFolio()))
        .term1Height(studentData == null ? null : studentData.getTerm1Height())
        .term2Height(studentData == null ? null : studentData.getTerm2Height())
        .term1Weight(studentData == null ? null : studentData.getTerm1Weight())
        .term2Weight(studentData == null ? null : studentData.getTerm2Weight())
        .competenciesList(getCompetencyDetails(section.getGradeSlug(), orgSlug, student))
        .selfAssessments(getSelfAssessmentDetails(section.getGradeSlug(), orgSlug, student))
        .peerAssessment(getPeerAssessmentDetails(section.getGradeSlug(), orgSlug, student))
        .parentsFeedbacks(getParentsFeedBacks(section.getGradeSlug(), orgSlug, student))
        .facilitator(getFacilitator(section.getGradeSlug(), orgSlug, student))
        .summarySheet(getSummarySheet(section.getGradeSlug(), orgSlug, student))
        .childBetter(getChildBetter(section.getGradeSlug(), orgSlug, student))
        .bloodGroup(studentData == null ? null : studentData.getBloodGroup())
        .dental(studentData == null ? null : studentData.getDental())
        .eyesightL(studentData == null ? null : studentData.getEyesightL())
        .eyesightR(studentData == null ? null : studentData.getEyesightR())
        .interestedActivities(studentData == null ? null : studentData.getInterestedActivities())
        .childSupport(studentData == null ? null : studentData.getChildSupport())
        .parentFacilitator(studentData == null ? null : studentData.getParentFacilitator())
        .build();
  }

  public List<ProgressCardDto.ChildBetter> getChildBetter(
      String gradeSlug, String orgSlug, Student student) {
    List<ProgressCardDto.ChildBetter> responseList = new ArrayList<>();

    if (student == null) {
      return responseList;
    }

    List<ChildBetter> childBetterList;
    childBetterList = childBetterRepository.findByOrgSlugAndGradeSlug(orgSlug, gradeSlug);

    if (childBetterList.isEmpty()) {
      return responseList;
    }

    childBetterList.forEach(
        childBetter -> {
          var studentData =
              childBetter.getStudents().stream()
                  .filter(x -> x.getStudentId().equals(student.getId()))
                  .findAny();

          responseList.add(
              ProgressCardDto.ChildBetter.builder()
                  .id(childBetter.getId())
                  .name(childBetter.getName())
                  .term1(studentData.map(ChildBetterStudents::getTerm1).orElse(null))
                  .term2(studentData.map(ChildBetterStudents::getTerm2).orElse(null))
                  .build());
        });

    return responseList;
  }

  public List<ProgressCardDto.SummarySheet> getSummarySheet(
      String gradeSlug, String orgSlug, Student student) {
    List<ProgressCardDto.SummarySheet> responseList = new ArrayList<>();

    if (student == null) {
      return responseList;
    }

    List<SummarySheet> summarySheetList;
    summarySheetList = summarySheetRepository.findByOrgSlugAndGradeSlug(orgSlug, gradeSlug);

    if (summarySheetList.isEmpty()) {
      return responseList;
    }

    summarySheetList.forEach(
        summarySheet -> {
          var studentData =
              summarySheet.getStudents().stream()
                  .filter(x -> x.getStudentId().equals(student.getId()))
                  .findAny();

          responseList.add(
              ProgressCardDto.SummarySheet.builder()
                  .id(summarySheet.getId())
                  .name(summarySheet.getName())
                  .term1(studentData.map(SummarySheetStudents::getTerm1).orElse(null))
                  .term2(studentData.map(SummarySheetStudents::getTerm2).orElse(null))
                  .skill(summarySheet.getSkill())
                  .build());
        });

    return responseList;
  }

  public ProgressCardDto.Facilitator getFacilitator(
      String gradeSlug, String orgSlug, Student student) {

    if (student == null) {
      return null;
    }
    List<Facilitator> facilitators =
        facilitatorRepository.findByOrgSlugAndGradeSlug(
            orgSlug, student.getSection().getGradeSlug());
    if (facilitators.isEmpty()) {
      return null;
    }
    var studentFacilitatorObservations =
        facilitatorObservationsRepository.findByStudentId(student.getId());
    return ProgressCardDto.Facilitator.builder()
        .skills(buildFacilitatorResponse(facilitators, student))
        .areaOfStrength(
            studentFacilitatorObservations
                .map(FacilitatorObservations::getAreaOfStrength)
                .orElse(null))
        .achievement(
            studentFacilitatorObservations
                .map(FacilitatorObservations::getAchievements)
                .orElse(null))
        .futureSteps(
            studentFacilitatorObservations
                .map(FacilitatorObservations::getFutureSteps)
                .orElse(null))
        .studentProgress(
            studentFacilitatorObservations
                .map(FacilitatorObservations::getStudentProgress)
                .orElse(null))
        .barrierOfSuccess(
            studentFacilitatorObservations
                .map(FacilitatorObservations::getBarrierOfSuccess)
                .orElse(null))
        .classFacilitatorRemarks(
            studentFacilitatorObservations
                .map(FacilitatorObservations::getClassFacilitatorRemarks)
                .orElse(null))
        .participation(
            studentFacilitatorObservations
                .map(FacilitatorObservations::getParticipation)
                .orElse(null))
        .principalRemarks(
            studentFacilitatorObservations
                .map(FacilitatorObservations::getPrincipalRemarks)
                .orElse(null))
        .build();
  }

  private List<ProgressCardDto.Skills> buildFacilitatorResponse(
      List<Facilitator> facilitators, Student student) {
    List<ProgressCardDto.Skills> responseList = new ArrayList<>();
    Map<String, List<Facilitator>> facilitatorMap =
        facilitators.stream().collect(Collectors.groupingBy(Facilitator::getSkill));
    facilitatorMap.forEach(
        (skill, facilitatorList) -> {
          List<ProgressCardDto.FacilitatorDetails> facilitatorDetails = new ArrayList<>();
          facilitatorList.forEach(
              data -> {
                var studentFacilitatorData =
                    facilitatorStudentsRepository.findByStudentIdAndFacilitator(
                        student.getId(), data);
                facilitatorDetails.add(
                    ProgressCardDto.FacilitatorDetails.builder()
                        .id(data.getId())
                        .name(data.getName())
                        .term1(
                            studentFacilitatorData.map(FacilitatorStudents::getTerm1).orElse(null))
                        .term2(
                            studentFacilitatorData.map(FacilitatorStudents::getTerm2).orElse(null))
                        .build());
              });
          responseList.add(
              ProgressCardDto.Skills.builder()
                  .skillName(skill)
                  .facilitatorDetails(facilitatorDetails)
                  .build());
        });

    return responseList;
  }

  public List<ProgressCardDto.ParentsFeedback> getParentsFeedBacks(
      String gradeSlug, String orgSlug, Student student) {
    List<ProgressCardDto.ParentsFeedback> responseList = new ArrayList<>();

    if (student == null) {
      return responseList;
    }

    List<ParentsFeedBack> parentsFeedBackList;
    parentsFeedBackList = parentsFeedBackRepository.findByOrgSlugAndGradeSlug(orgSlug, gradeSlug);

    if (parentsFeedBackList.isEmpty()) {
      return responseList;
    }

    parentsFeedBackList.forEach(
        feedBack -> {
          var studentData =
              feedBack.getStudents().stream()
                  .filter(x -> x.getStudentId().equals(student.getId()))
                  .findAny();

          responseList.add(
              ProgressCardDto.ParentsFeedback.builder()
                  .id(feedBack.getId())
                  .name(feedBack.getName())
                  .term1(studentData.map(ParentsFeedBackStudents::getTerm1).orElse(null))
                  .term2(studentData.map(ParentsFeedBackStudents::getTerm2).orElse(null))
                  .build());
        });

    return responseList;
  }

  public List<ProgressCardDto.PeerAssessment> getPeerAssessmentDetails(
      String gradeSlug, String orgSlug, Student student) {
    List<ProgressCardDto.PeerAssessment> responseList = new ArrayList<>();

    if (student == null) {
      return responseList;
    }

    List<PeerAssessments> peerAssessmentList;
    peerAssessmentList = peerAssessmentRepository.findByOrgSlugAndGradeSlug(orgSlug, gradeSlug);

    if (peerAssessmentList.isEmpty()) {
      return responseList;
    }

    peerAssessmentList.forEach(
        competency -> {
          var studentData =
              competency.getStudents().stream()
                  .filter(x -> x.getStudentId().equals(student.getId()))
                  .findAny();

          responseList.add(
              ProgressCardDto.PeerAssessment.builder()
                  .id(competency.getId())
                  .name(competency.getName())
                  .term1(studentData.map(PeerAssessmentStudents::getTerm1).orElse(null))
                  .term2(studentData.map(PeerAssessmentStudents::getTerm2).orElse(null))
                  .descriptionName(competency.getDescriptionName())
                  .build());
        });

    return responseList;
  }

  public List<ProgressCardDto.SelfAssessment> getSelfAssessmentDetails(
      String gradeSlug, String orgSlug, Student student) {
    List<ProgressCardDto.SelfAssessment> responseList = new ArrayList<>();

    if (student == null) {
      return responseList;
    }

    List<SelfAssessment> selfAssessmentList;
    selfAssessmentList = selfAssessmentRepository.findByOrgSlugAndGradeSlug(orgSlug, gradeSlug);

    if (selfAssessmentList.isEmpty()) {
      return responseList;
    }

    selfAssessmentList.forEach(
        competency -> {
          var studentData =
              competency.getStudents().stream()
                  .filter(x -> x.getStudentId().equals(student.getId()))
                  .findAny();

          responseList.add(
              ProgressCardDto.SelfAssessment.builder()
                  .id(competency.getId())
                  .name(competency.getName())
                  .term1(studentData.map(SelfAssessmentStudents::getTerm1).orElse(null))
                  .term2(studentData.map(SelfAssessmentStudents::getTerm2).orElse(null))
                  .descriptionName(competency.getDescriptionName())
                  .build());
        });

    return responseList;
  }

  public List<ProgressCardDto.Competencies> getCompetencyDetails(
      String gradeSlug, String orgSlug, Student student) {

    List<ProgressCardDto.Competencies> responseList = new ArrayList<>();

    List<Competencies> competenciesList =
        competencyRepository.findByOrgSlugAndGradeSlug(orgSlug, gradeSlug);

    if (competenciesList.isEmpty()) {
      return responseList;
    }
    var subjects = competenciesList.stream().map(Competencies::getSubjectSlug).distinct().toList();
    subjects.forEach(
        subject -> {
          var competencySubjects =
              competenciesList.stream().filter(x -> x.getSubjectSlug().equals(subject)).toList();
          var subjectMetaData =
              subjectsMetaDataRepository
                  .findByOrgSlugAndWexlSubjectSlugAndNameAndGradeSlugAndBoardSlugAndStatusAndDeletedAtIsNull(
                      orgSlug,
                      competencySubjects.getFirst().getSubjectSlug(),
                      competencySubjects.getFirst().getSubjectName(),
                      gradeSlug,
                      "cbse",
                      true);

          responseList.add(
              ProgressCardDto.Competencies.builder()
                  .id(competencySubjects.getFirst().getId())
                  .subjectName(competencySubjects.getFirst().getSubjectName())
                  .subjectSlug(competencySubjects.getFirst().getSubjectSlug())
                  .category(subjectMetaData != null ? subjectMetaData.getCategoryEnum() : null)
                  .skills(buildCompetencyResponse(competencySubjects, student))
                  .build());
        });
    return responseList;
  }

  private List<ProgressCardDto.Skill> buildCompetencyResponse(
      List<Competencies> competenciesList, Student student) {
    List<ProgressCardDto.Skill> responseList = new ArrayList<>();
    var competenciesSkillMap =
        competenciesList.stream()
            .filter(c -> Objects.nonNull(c.getSkill()))
            .collect(Collectors.groupingBy(Competencies::getSkill));
    competenciesSkillMap.forEach(
        (skill, parameters) -> {
          List<ProgressCardDto.Details> details =
              parameters.stream()
                  .map(
                      parameter -> {
                        var studentData =
                            parameter.getStudents().stream()
                                .filter(x -> x.getStudentId().equals(student.getId()))
                                .findAny();
                        return ProgressCardDto.Details.builder()
                            .id(parameter.getId())
                            .name(parameter.getName())
                            .term1(studentData.map(CompetenciesStudents::getTerm1).orElse(null))
                            .term2(studentData.map(CompetenciesStudents::getTerm2).orElse(null))
                            .build();
                      })
                  .toList();
          responseList.add(
              ProgressCardDto.Skill.builder().skillName(skill).competencyDetails(details).build());
        });
    return responseList;
  }

  public String fetchImage(String path) {
    return (path == null || path.isEmpty())
        ? null
        : storageService.generatePreSignedUrlForFetchImage(path);
  }
}
