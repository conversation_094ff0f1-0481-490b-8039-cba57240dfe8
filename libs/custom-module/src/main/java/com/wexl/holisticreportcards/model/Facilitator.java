package com.wexl.holisticreportcards.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "gillco_pre_primary_facilitator")
public class Facilitator extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "grade_slug")
  private String gradeSlug;

  private String name;

  private String skill;

  @Column(name = "org_slug")
  private String orgSlug;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "gillco_pre_primary_facilitator_id")
  private List<FacilitatorStudents> facilitatorStudents;
}
